<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from '@/utils/toast'
import { getMediaUserList, deleteMediaUser, type MediaUserAccount } from '@/api/user'
import { getMediaList, type MediaPlatform } from '@/api/common'
import { showConfirmDialog } from 'vant'

// 路由实例
const router = useRouter()
const route = useRoute()

// 账号数据
const accounts = ref<MediaUserAccount[]>([])

// 媒体平台数据（用于显示平台名称）
const mediaList = ref<MediaPlatform[]>([])

// 加载状态
const loading = ref(false)

// 返回上一页
const goBack = () => {
  // 获取来源信息
  const fromPage = route.query.from as string

  // 检查是否有历史记录可以返回
  if (window.history.length > 1 && document.referrer) {
    router.back()
  } else {
    // 没有历史记录，根据来源跳转到相应页面
    if (fromPage === 'user') {
      router.replace('/user')
    } else {
      // 默认跳转到个人中心页面
      router.replace('/user')
    }
  }
}

// 添加新账号
const addAccount = () => {
  router.push('/accounts/add?from=accounts')
}



// 获取媒体平台名称
const getMediaName = (mediaId: number): string => {
  const media = mediaList.value.find(item => item.id === mediaId)
  return media ? media.name : '未知平台'
}

// 加载媒体平台列表
const loadMediaList = async () => {
  try {
    const list = await getMediaList()
    mediaList.value = list
  } catch (error) {
    console.error('获取媒体平台列表失败:', error)
  }
}

// 加载账号数据
const loadAccounts = async () => {
  loading.value = true
  try {
    const response = await getMediaUserList()
    if (response.code === 1) {
      accounts.value = response.data || []
    } else {
      showToast(response.msg || '获取账号列表失败')
    }
  } catch (error) {
    console.error('获取账号列表失败:', error)
    showToast('获取账号列表失败，请重试')
  } finally {
    loading.value = false
  }
}

// 删除账号
const deleteAccount = async (account: MediaUserAccount) => {
  try {
    await showConfirmDialog({
      title: '确认删除',
      message: `确定要删除账号"${account.ncname}"吗？此操作不可撤销。`,
      confirmButtonText: '删除',
      cancelButtonText: '取消',
      confirmButtonColor: '#ff6b35'
    })

    // 执行删除操作
    const response = await deleteMediaUser({ mediauid: account.id })

    if (response.code === 1) {
      showToast('删除成功')
      // 重新加载账号列表
      await loadAccounts()
    } else {
      showToast(response.msg || '删除失败')
    }
  } catch (error: any) {
    // 用户取消删除或其他错误
    if (error !== 'cancel') {
      console.error('删除账号失败:', error)
      showToast('删除失败，请重试')
    }
  }
}

// 页面加载
onMounted(async () => {
  // 先加载媒体平台列表，再加载账号数据
  await loadMediaList()
  await loadAccounts()
})
</script>

<template>
  <div class="accounts-container">
    <!-- 导航栏 -->
    <van-nav-bar 
      title="账号库" 
      left-arrow
      fixed 
      placeholder
      background="#ff6b35"
      color="#fff"
      @click-left="goBack"
    />
    
    <!-- 提示信息 -->
    <div class="tip-section">
      <van-notice-bar
        left-icon="info-o"
        text="可在对应媒体平台发布文章、帖子、短视频等内容的账号"
        background="#fff3e0"
        color="#ff8f00"
      />
    </div>
    
    <!-- 账号列表 -->
    <div class="accounts-list">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state">
        <van-loading size="24px" vertical>加载中...</van-loading>
      </div>

      <!-- 账号列表 -->
      <div
        v-else
        v-for="account in accounts"
        :key="account.id"
        class="account-item"
      >
        <div class="account-info">
          <div class="account-name">{{ account.ncname }}</div>
          <div class="account-platform">{{ getMediaName(account.meidaid) }}</div>
        </div>
        <div class="account-actions">
          <div class="delete-btn" @click="deleteAccount(account)">
            <van-icon name="delete-o" size="16" color="#ff6b35" />
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && accounts.length === 0" class="empty-state">
        <van-empty
          image="search"
          description="暂无账号信息"
        />
      </div>
    </div>
    
    <!-- 浮动添加按钮 -->
    <div class="floating-add-btn" @click="addAccount">
      <van-icon name="plus" size="24" color="#fff" />
    </div>
    

  </div>
</template>

<style scoped>
.accounts-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 80px;
}

.tip-section {
  padding: 12px 16px;
}

.accounts-list {
  padding: 0 16px;
}

.account-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.account-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.account-info {
  flex: 1;
}

.account-name {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 4px;
}

.account-platform {
  font-size: 13px;
  color: #969799;
}

.account-actions {
  display: flex;
  align-items: center;
}

.delete-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #fff2f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.delete-btn:hover {
  background: #ffebe6;
  transform: scale(1.1);
}

.loading-state {
  padding: 40px 20px;
  text-align: center;
}

.empty-state {
  padding: 40px 20px;
  text-align: center;
}

.floating-add-btn {
  position: fixed;
  bottom: 100px;
  right: 20px;
  width: 56px;
  height: 56px;
  background: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
  z-index: 999;
}

.floating-add-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(255, 107, 53, 0.5);
}

.floating-add-btn:active {
  transform: scale(0.95);
}



/* 自定义导航栏样式 */
:deep(.van-nav-bar) {
  background: linear-gradient(90deg, #ff6b35, #ff8c69);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar__left .van-icon) {
  color: #fff;
}


</style>
