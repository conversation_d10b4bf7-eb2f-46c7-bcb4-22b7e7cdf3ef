<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { showToast } from '@/utils/toast'
import { showLoadingToast, closeToast, showConfirmDialog } from 'vant'
import { getTaskDetail, submitTask, type Task } from '@/api/tasks'

// 路由信息
const route = useRoute()
const router = useRouter()

// 任务详情ID（来自getrenwulqlist接口的taskid字段，用于获取详情）
const taskDetailId = computed(() => route.params.id as string)

// 提交任务ID（来自getrenwulqlist接口的id字段，用于任务提交）
const submitTaskId = computed(() => route.query.submitId as string || route.params.id as string)

// 检查是否来自申诉（未通过列表）
const isAppeal = computed(() => route.query.action === 'resubmit')

// 任务详情
const taskDetail = ref<Task | null>(null)

// 表单数据
const formData = ref({
  publishLink: '',
  screenshot: [] as any[],
  otherProof: ''
})

// 加载状态
const loading = ref(false)
const submitting = ref(false)

// 获取任务详情
const fetchTaskDetail = async () => {
  try {
    const task = await getTaskDetail(taskDetailId.value)
    taskDetail.value = task
  } catch (error: any) {
    console.error('获取任务详情失败:', error)
    if (error && error.message === "未登录") {
      return
    }
    showToast('获取任务详情失败')
  }
}



// 处理截图上传
const handleScreenshotUpload = (file: File) => {
  // Vant上传组件需要返回false来阻止自动上传
  return false
}

// 提交任务
const handleSubmit = async () => {
  // 检查是否已获取到提交任务ID
  if (!submitTaskId.value) {
    showToast('任务信息加载中，请稍后重试')
    return
  }

  // 表单验证
  if (!formData.value.publishLink.trim()) {
    showToast('请输入发布链接')
    return
  }

  // URL格式验证
  const urlPattern = /^https?:\/\/.+/
  if (!urlPattern.test(formData.value.publishLink.trim())) {
    showToast('请输入有效的链接地址')
    return
  }

  // 截图必填验证
  if (!formData.value.screenshot || formData.value.screenshot.length === 0) {
    showToast('请上传任务完成的截图')
    return
  }

  try {
    const confirmMessage = isAppeal.value
      ? '确定要重新提交申诉吗？提交后将重新等待审核。'
      : '确定要提交这个任务吗？提交后将等待审核。'

    await showConfirmDialog({
      title: '确认提交',
      message: confirmMessage,
      confirmButtonText: '确认提交',
      cancelButtonText: '再检查一下'
    })

    submitting.value = true
    showLoadingToast({
      message: '提交中...',
      forbidClick: true,
    })

    // 获取上传的截图文件（必填）
    const screenshotFile = formData.value.screenshot[0].file

    const params = {
      renwuid: submitTaskId.value,
      stashensu: isAppeal.value ? 1 : 0, // 根据来源确定是否为申诉
      sublink: formData.value.publishLink.trim(),
      subpic: screenshotFile, // 必填参数
      subtext: formData.value.otherProof.trim() || undefined
    }

    const response = await submitTask(params)

    if (response.code === 1) {
      showToast('提交成功！')
      // 返回到我的任务页面
      router.replace('/my-tasks')
    } else {
      showToast(response.msg || '提交失败，请重试')
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('提交任务失败:', error)
      if (error && error.message === "未登录") {
        return
      }
      showToast(error.message || '提交失败，请重试')
    }
  } finally {
    submitting.value = false
    closeToast()
  }
}

// 返回上一页
const goBack = () => {
  // 检查是否有历史记录可以返回
  if (window.history.length > 1 && document.referrer) {
    router.back()
  } else {
    // 没有历史记录，跳转到任务详情页面
    router.replace(`/task-detail/${taskDetailId.value}`)
  }
}

// 页面加载
onMounted(async () => {
  loading.value = true
  try {
    // 加载任务详情
    await fetchTaskDetail()
  } finally {
    loading.value = false
  }
})
</script>

<template>
  <div class="submit-task-container">
    <!-- 导航栏 -->
    <van-nav-bar
      :title="isAppeal ? '申诉任务' : '提交任务'"
      left-arrow
      left-text="返回"
      @click-left="goBack"
      fixed
      placeholder
      background="#ff6b35"
      color="#fff"
    />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <van-loading size="24px" vertical>加载中...</van-loading>
    </div>

    <!-- 主要内容 -->
    <div v-else class="content">

      <!-- 提交表单 -->
      <div class="form-container">
        <!-- 发布链接 -->
        <div class="form-item">
          <div class="form-label">发布链接 <span class="required">*</span></div>
          <van-field
            v-model="formData.publishLink"
            type="url"
            placeholder="请输入发布后的链接地址"
            clearable
            :border="false"
            class="link-input"
          />
          <div class="form-tip">请确保链接可以正常访问</div>
        </div>

        <!-- 提交截图 -->
        <div class="form-item">
          <div class="form-label">提交截图 <span class="required">*</span></div>
          <van-uploader
            v-model="formData.screenshot"
            :max-count="1"
            :before-upload="handleScreenshotUpload"
            accept="image/*"
            :preview-size="80"
            upload-text="上传截图"
          />
          <div class="form-tip">请上传任务完成的截图证明</div>
        </div>

        <!-- 其他证明 -->
        <div class="form-item">
          <div class="form-label">其他证明</div>
          <van-field
            v-model="formData.otherProof"
            type="textarea"
            placeholder="请输入其他证明信息（可选）"
            rows="3"
            autosize
            :border="false"
            class="textarea-input"
          />
          <div class="form-tip">可以补充说明任务完成情况</div>
        </div>
      </div>

      <!-- 提交按钮 -->
      <div class="submit-container">
        <van-button
          type="primary"
          block
          round
          size="large"
          @click="handleSubmit"
          :loading="submitting"
          color="#ff6b35"
        >
          {{ isAppeal ? '提交申诉' : '提交任务' }}
        </van-button>
      </div>
    </div>


  </div>
</template>

<style scoped>
.submit-task-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #ff6b35 0%, #f7f8fa 25%);
  padding-bottom: 20px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.content {
  padding: 12px 16px;
}

/* 任务信息卡片 */
.task-info-card {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 4px 20px rgba(255, 107, 53, 0.1);
}

.task-title {
  font-size: 18px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12px;
  line-height: 1.4;
}

.task-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-platform {
  font-size: 14px;
  color: #646566;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.task-score {
  font-size: 16px;
  font-weight: 600;
  color: #ff6b35;
}

/* 表单容器 */
.form-container {
  background: #fff;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.form-item {
  margin-bottom: 20px;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12px;
}

.required {
  color: #ff6b35;
  margin-left: 4px;
}



/* 链接输入框 */
.link-input {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 0;
}

:deep(.link-input .van-field__control) {
  padding: 16px;
  font-size: 16px;
  line-height: 1.4;
}

.form-tip {
  font-size: 12px;
  color: #969799;
  margin-top: 8px;
  padding-left: 4px;
}

/* 文本域输入框 */
.textarea-input {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 0;
}

:deep(.textarea-input .van-field__control) {
  padding: 16px;
  font-size: 16px;
  line-height: 1.4;
  min-height: 80px;
}

/* 上传组件样式 */
:deep(.van-uploader) {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
}

:deep(.van-uploader__upload) {
  background: #fff;
  border: 2px dashed #ddd;
  border-radius: 8px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-size: 12px;
  color: #969799;
  transition: all 0.3s ease;
}

:deep(.van-uploader__upload:hover) {
  border-color: #ff6b35;
  color: #ff6b35;
}

:deep(.van-uploader__upload-icon) {
  font-size: 24px;
  margin-bottom: 4px;
}

/* 提交按钮 */
.submit-container {
  padding: 0 4px;
}



/* 自定义导航栏样式 */
:deep(.van-nav-bar) {
  background: linear-gradient(90deg, #ff6b35, #ff8c69);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar__left) {
  color: #fff;
}

:deep(.van-nav-bar__arrow) {
  color: #fff;
}

:deep(.van-nav-bar__text) {
  color: #fff;
}
</style>
