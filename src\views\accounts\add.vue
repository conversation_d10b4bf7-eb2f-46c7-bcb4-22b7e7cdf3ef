<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast } from '@/utils/toast'
import { getMediaList, type MediaPlatform } from '@/api/common'
import { addMediaUser, type AddMediaUserParams } from '@/api/user'

// 路由实例
const router = useRouter()
const route = useRoute()

// 表单数据
const formData = reactive({
  platform: '',
  platformId: 0,
  accountName: ''
})

// 平台选项
const platformOptions = ref<{ text: string; value: number }[]>([])

// 加载状态
const loadingPlatforms = ref(false)

// 显示平台选择器
const showPlatformPicker = ref(false)

// 加载状态
const loading = ref(false)

// 返回上一页
const goBack = () => {
  // 获取来源信息
  const fromPage = route.query.from as string

  // 检查是否有历史记录可以返回
  if (window.history.length > 1 && document.referrer) {
    router.back()
  } else {
    // 没有历史记录，根据来源跳转到相应页面
    if (fromPage === 'accounts') {
      router.replace('/accounts')
    } else {
      // 默认跳转到账号库页面
      router.replace('/accounts')
    }
  }
}

// 获取媒体平台列表
const loadMediaPlatforms = async () => {
  loadingPlatforms.value = true
  try {
    const platforms = await getMediaList()
    platformOptions.value = platforms.map(platform => ({
      text: platform.name,
      value: platform.id
    }))
  } catch (error) {
    console.error('获取平台列表失败:', error)
    showToast('获取平台列表失败，请重试')
  } finally {
    loadingPlatforms.value = false
  }
}

// 选择平台
const onPlatformConfirm = ({ selectedOptions }: any) => {
  const selectedOption = selectedOptions[0]
  if (selectedOption) {
    formData.platform = selectedOption.text
    formData.platformId = selectedOption.value
  }
  showPlatformPicker.value = false
}

// 保存账号
const saveAccount = async () => {
  // 表单验证
  if (!formData.platform) {
    showToast('请选择发布平台')
    return
  }

  if (!formData.accountName.trim()) {
    showToast('请输入账号昵称')
    return
  }

  if (!formData.platformId) {
    showToast('平台信息异常，请重新选择')
    return
  }

  loading.value = true

  try {
    // 调用API保存账号信息
    const params: AddMediaUserParams = {
      meidaid: formData.platformId,
      ncname: formData.accountName.trim()
    }

    const response = await addMediaUser(params)

    if (response.code === 1) {
      showToast('添加成功')
      // 保存成功后返回账号库页面
      router.replace('/accounts')
    } else {
      // API返回失败信息
      showToast(response.msg || '添加失败，请重试')
    }

  } catch (error) {
    console.error('保存失败:', error)
    showToast('保存失败，请重试')
  } finally {
    loading.value = false
  }
}

// 页面加载时获取平台列表
onMounted(() => {
  loadMediaPlatforms()
})
</script>

<template>
  <div class="add-account-container">
    <!-- 导航栏 -->
    <van-nav-bar 
      title="添加发布账号" 
      left-arrow
      fixed 
      placeholder
      background="#ff6b35"
      color="#fff"
      @click-left="goBack"
    />
    
    <!-- 表单内容 -->
    <div class="form-container">
      <!-- 平台选择 -->
      <div class="form-section">
        <div class="section-title">选择执行任务平台</div>
        <van-field
          v-model="formData.platform"
          readonly
          clickable
          :placeholder="loadingPlatforms ? '加载平台列表中...' : '请选择平台'"
          :disabled="loadingPlatforms"
          @click="!loadingPlatforms && (showPlatformPicker = true)"
        >
          <template #right-icon>
            <van-loading v-if="loadingPlatforms" size="16" />
            <van-icon v-else name="arrow-down" />
          </template>
        </van-field>
      </div>
      
      <!-- 账号昵称输入 -->
      <div class="form-section">
        <div class="section-title">
          输入{{ formData.platform || '所选平台' }}的昵称
        </div>
        <van-field
          v-model="formData.accountName"
          placeholder="请输入您的个人账号昵称信息"
          maxlength="50"
          show-word-limit
          clearable
        />
      </div>
    </div>
    
    <!-- 保存按钮 -->
    <div class="save-button">
      <van-button
        type="primary"
        block
        round
        size="large"
        :loading="loading"
        @click="saveAccount"
        color="#ff6b35"
      >
        {{ loading ? '保存中...' : '保存' }}
      </van-button>
    </div>
    
    <!-- 平台选择器 -->
    <van-popup v-model:show="showPlatformPicker" position="bottom">
      <van-picker
        :columns="platformOptions"
        @confirm="onPlatformConfirm"
        @cancel="showPlatformPicker = false"
      />
    </van-popup>
  </div>
</template>

<style scoped>
.add-account-container {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-bottom: 100px;
}

.form-container {
  padding: 20px 16px;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 14px;
  color: #323233;
  margin-bottom: 12px;
  font-weight: 500;
}

.save-button {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  z-index: 999;
}

/* 自定义导航栏样式 */
:deep(.van-nav-bar) {
  background: linear-gradient(90deg, #ff6b35, #ff8c69);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar__left .van-icon) {
  color: #fff;
}

/* 自定义字段样式 */
:deep(.van-field) {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 1px;
}

:deep(.van-field__control) {
  font-size: 14px;
}

:deep(.van-field__control::placeholder) {
  color: #c8c9cc;
}

/* 自定义按钮样式 */
:deep(.van-button--primary) {
  background: #ff6b35;
  border-color: #ff6b35;
  font-size: 16px;
  font-weight: 600;
}
</style>
