<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showLoadingToast, closeToast } from 'vant'
import { showToast } from '@/utils/toast'
import { useUserStore } from '@/stores/user'
import { bindWechat } from '@/api/user'

// 路由实例
const router = useRouter()
const route = useRoute()

// 用户状态
const userStore = useUserStore()

// 页面状态
const isLoading = ref(false)
const bindingStatus = ref<'waiting' | 'success' | 'failed'>('waiting')

// 微信配置
const WECHAT_APPID = 'wxa503380a549ecda3'

// 检查是否在微信环境中
const isWechatBrowser = () => {
  const ua = navigator.userAgent.toLowerCase()
  return ua.includes('micromessenger')
}

// 微信授权绑定
const wechatOAuthBind = () => {
  // 构建微信授权URL
  const redirectUri = encodeURIComponent(window.location.href)
  const state = 'bind_wechat_' + Date.now()
  const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${WECHAT_APPID}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_userinfo&state=${state}#wechat_redirect`

  // 跳转到微信授权页面
  window.location.href = authUrl
}



// 返回上一页
const goBack = () => {
  // 获取来源信息
  const fromPage = route.query.from as string

  // 检查是否有历史记录可以返回
  if (window.history.length > 1 && document.referrer) {
    router.back()
  } else {
    // 没有历史记录，根据来源跳转到相应页面
    if (fromPage === 'user') {
      router.replace('/user')
    } else {
      // 默认跳转到个人中心页面
      router.replace('/user')
    }
  }
}

// 初始化绑定流程
const initBindingFlow = async () => {
  // 检查URL中是否有微信授权返回的code
  const urlParams = new URLSearchParams(window.location.search)
  const code = urlParams.get('code')
  const state = urlParams.get('state')

  if (code && state && state.startsWith('bind_wechat_')) {
    // 处理微信授权返回
    await handleWechatAuthCallback(code)
    return
  }

  // 检查是否在微信环境中
  if (!isWechatBrowser()) {
    // 非微信环境，提示用户在微信中打开
    bindingStatus.value = 'failed'
    showToast('请在微信中打开此页面进行绑定')
    return
  }

  // 在微信环境中，直接启动授权流程
  wechatOAuthBind()
}

// 处理微信授权回调
const handleWechatAuthCallback = async (code: string) => {
  isLoading.value = true

  try {
    const response = await bindWechat({ code })

    if (response.code === 1) {
      bindingStatus.value = 'success'
      showToast('微信绑定成功！')

      // 重新加载用户信息
      await userStore.fetchMemberInfo()

      // 延迟跳转回个人中心
      setTimeout(() => {
        router.replace('/user')
      }, 1500)
    } else {
      bindingStatus.value = 'failed'
      showToast(response.msg || '绑定失败')
    }
  } catch (error) {
    console.error('微信绑定失败:', error)
    bindingStatus.value = 'failed'
    showToast('绑定失败，请稍后重试')
  } finally {
    isLoading.value = false
  }
}

// 页面加载时初始化
onMounted(() => {
  initBindingFlow()
})
</script>

<template>
  <div class="bind-wechat-container">
    <!-- 导航栏 -->
    <van-nav-bar 
      title="绑定微信" 
      left-arrow
      fixed 
      placeholder
      background="#ff6b35"
      color="#fff"
      @click-left="goBack"
    />
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 说明文字 -->
      <div class="description">
        <h3>绑定微信账号</h3>
        <p v-if="bindingStatus === 'waiting'">
          正在跳转到微信授权页面，请稍候...
        </p>
        <p v-else-if="bindingStatus === 'failed'">
          请在微信中打开此页面进行绑定操作。
        </p>
        <p v-else>
          绑定微信后，您可以使用微信快速登录，享受更便捷的服务体验。
        </p>
      </div>
      
      <!-- 内容区域 -->
      <div class="content-section">
        <div v-if="isLoading" class="loading-container">
          <van-loading size="24" color="#ff6b35" />
          <p class="loading-text">正在处理...</p>
        </div>

        <div v-else-if="bindingStatus === 'success'" class="success-container">
          <van-icon name="checked" size="48" color="#07c160" />
          <p class="success-text">绑定成功！</p>
          <p class="success-desc">正在跳转...</p>
        </div>

        <div v-else-if="bindingStatus === 'failed'" class="failed-container">
          <van-icon name="cross" size="48" color="#ee0a24" />
          <p class="failed-text">绑定失败</p>
          <p class="failed-desc">请在微信中打开此页面，或联系客服</p>
          <van-button
            type="primary"
            round
            @click="initBindingFlow"
            color="#ff6b35"
            style="margin-top: 16px;"
          >
            重新尝试
          </van-button>
        </div>

        <div v-else class="oauth-container">
          <van-loading size="24" color="#ff6b35" />
          <p class="oauth-text">正在跳转到微信授权...</p>
          <p class="oauth-desc">如果没有自动跳转，请点击下方按钮</p>
          <van-button
            type="primary"
            round
            @click="wechatOAuthBind"
            color="#ff6b35"
            style="margin-top: 16px;"
          >
            手动授权
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.bind-wechat-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.content {
  padding: 20px 16px;
}

.description {
  text-align: center;
  margin-bottom: 32px;
}

.description h3 {
  font-size: 20px;
  font-weight: 600;
  color: #323233;
  margin: 0 0 12px 0;
}

.description p {
  font-size: 14px;
  color: #969799;
  line-height: 1.5;
  margin: 0;
}

.content-section {
  background: #fff;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.loading-text {
  margin: 16px 0 0 0;
  font-size: 14px;
  color: #969799;
}

.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.success-text {
  font-size: 18px;
  font-weight: 600;
  color: #07c160;
  margin: 16px 0 8px 0;
}

.success-desc {
  font-size: 14px;
  color: #969799;
  margin: 0;
}

.failed-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.failed-text {
  font-size: 18px;
  font-weight: 600;
  color: #ee0a24;
  margin: 16px 0 8px 0;
}

.failed-desc {
  font-size: 14px;
  color: #969799;
  margin: 0;
}

.oauth-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.oauth-text {
  font-size: 16px;
  font-weight: 600;
  color: #323233;
  margin: 16px 0 8px 0;
}

.oauth-desc {
  font-size: 14px;
  color: #969799;
  margin: 0;
  text-align: center;
}



/* 自定义导航栏样式 */
:deep(.van-nav-bar) {
  background: linear-gradient(90deg, #ff6b35, #ff8c69);
}

:deep(.van-nav-bar__title) {
  color: #fff;
  font-weight: 600;
}

:deep(.van-nav-bar__left .van-icon) {
  color: #fff;
}
</style>
